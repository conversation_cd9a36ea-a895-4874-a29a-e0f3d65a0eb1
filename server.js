// Simple Express server for bansheeblast Library API
const express = require('express');
const app = express();
const cors = require('cors');  // Import the cors middleware
const PORT = process.env.PORT || 3001;

app.use(cors());  // Enable CORS for all routes
app.use(express.static('.')); // Serve static files from current directory

app.use(express.json());

// Sample track data for demo - Enhanced with popular artists for better search results
const sampleTracks = {
  101: { id: 101, title: "Blinding Lights", artist: "The Weeknd", album: "After Hours", duration: "3:20", cover: "imgs/album-01.png", genre: "Pop", year: 2020, plays: 2500 },
  102: { id: 102, title: "Save Your Tears", artist: "The Weeknd", album: "After Hours", duration: "3:35", cover: "imgs/album-02.png", genre: "Pop", year: 2020, plays: 1890 },
  103: { id: 103, title: "Can't Feel My Face", artist: "The Weeknd", album: "Beauty Behind the Madness", duration: "3:35", cover: "imgs/album-03-B.png", genre: "Pop", year: 2015, plays: 2100 },
  104: { id: 104, title: "Starboy", artist: "The Weeknd", album: "Starboy", duration: "3:50", cover: "imgs/album-04-B.png", genre: "Pop", year: 2016, plays: 1980 },
  105: { id: 105, title: "Anti-Hero", artist: "Taylor Swift", album: "Midnights", duration: "3:20", cover: "imgs/album-01.png", genre: "Pop", year: 2022, plays: 2200 },
  106: { id: 106, title: "As It Was", artist: "Harry Styles", album: "Harry's House", duration: "2:47", cover: "imgs/album-02.png", genre: "Pop", year: 2022, plays: 1920 },
  107: { id: 107, title: "Heat Waves", artist: "Glass Animals", album: "Dreamland", duration: "3:58", cover: "imgs/album-03-B.png", genre: "Indie", year: 2020, plays: 1800 },
  108: { id: 108, title: "Stay", artist: "The Kid LAROI & Justin Bieber", album: "F*CK LOVE 3", duration: "2:21", cover: "imgs/album-04-B.png", genre: "Pop", year: 2021, plays: 1720 },
  109: { id: 109, title: "Good 4 U", artist: "Olivia Rodrigo", album: "SOUR", duration: "2:58", cover: "imgs/album-01.png", genre: "Pop", year: 2021, plays: 1650 },
  110: { id: 110, title: "Levitating", artist: "Dua Lipa", album: "Future Nostalgia", duration: "3:23", cover: "imgs/album-02.png", genre: "Pop", year: 2020, plays: 1850 },
  111: { id: 111, title: "Watermelon Sugar", artist: "Harry Styles", album: "Fine Line", duration: "2:54", cover: "imgs/album-03-B.png", genre: "Pop", year: 2020, plays: 1600 },
  112: { id: 112, title: "Drivers License", artist: "Olivia Rodrigo", album: "SOUR", duration: "4:02", cover: "imgs/album-04-B.png", genre: "Pop", year: 2021, plays: 1750 }
};

// Example in-memory data (replace with DB later) - Enhanced with more realistic data
let userLibrary = {
  playlists: [
    {
      id: 1,
      name: 'Favorites',
      tracks: [101, 102, 104, 107],
      description: 'My all-time favorite tracks',
      cover: 'imgs/playlist-01.png',
      created: '2024-01-15',
      lastModified: '2024-06-20'
    },
    {
      id: 2,
      name: 'Chill Vibes',
      tracks: [103, 105, 109],
      description: 'Perfect for relaxing and unwinding',
      cover: 'imgs/playlist-02.png',
      created: '2024-02-10',
      lastModified: '2024-06-18'
    },
    {
      id: 3,
      name: 'Workout Mix',
      tracks: [106, 101, 110, 103],
      description: 'High energy tracks for the gym',
      cover: 'imgs/playlist-03.png',
      created: '2024-03-05',
      lastModified: '2024-06-25'
    },
    {
      id: 4,
      name: 'Night Drive',
      tracks: [107, 106, 104],
      description: 'Perfect soundtrack for late night drives',
      cover: 'imgs/playlist-04.png',
      created: '2024-04-12',
      lastModified: '2024-06-22'
    },
    {
      id: 5,
      name: 'Study Focus',
      tracks: [102, 109, 104],
      description: 'Ambient tracks for concentration',
      cover: 'imgs/playlist-05.png',
      created: '2024-05-08',
      lastModified: '2024-06-15'
    }
  ],
  likedSongs: [101, 104, 105, 107, 109],
  recentlyPlayed: [103, 110, 101, 106, 108, 111],
  topArtists: ['The Weeknd', 'Taylor Swift', 'Harry Styles', 'Olivia Rodrigo'],
  stats: {
    totalPlaylists: 5,
    totalLikedSongs: 5,
    totalListeningTime: '24h 32m',
    topGenre: 'Electronic'
  }
};

// Get all playlists
app.get('/api/library', (req, res) => {
    res.json(userLibrary);
  });


// Get track details
app.get('/api/tracks/:id', (req, res) => {
  const trackId = parseInt(req.params.id);
  const track = sampleTracks[trackId];
  if (track) {
    res.json(track);
  } else {
    res.status(404).json({ error: 'Track not found' });
  }
});

// Get all playlists
app.get('/api/library/playlists', (req, res) => {
  res.json(userLibrary.playlists);
});

// Get liked songs with track details
app.get('/api/library/liked', (req, res) => {
  const likedTracksWithDetails = userLibrary.likedSongs.map(trackId => sampleTracks[trackId]).filter(Boolean);
  res.json(likedTracksWithDetails);
});

// Get recently played tracks
app.get('/api/library/recent', (req, res) => {
  const recentTracksWithDetails = userLibrary.recentlyPlayed.map(trackId => sampleTracks[trackId]).filter(Boolean);
  res.json(recentTracksWithDetails);
});

// Get user stats
app.get('/api/library/stats', (req, res) => {
  res.json(userLibrary.stats);
});

// Get top artists
app.get('/api/library/artists', (req, res) => {
  res.json(userLibrary.topArtists);
});

// Search library
app.get('/api/library/search', (req, res) => {
  const query = req.query.q?.toLowerCase() || '';
  if (!query) {
    return res.json({ tracks: [], playlists: [] });
  }

  // Search tracks
  const matchingTracks = Object.values(sampleTracks).filter(track =>
    track.title.toLowerCase().includes(query) ||
    track.artist.toLowerCase().includes(query) ||
    track.album.toLowerCase().includes(query) ||
    track.genre.toLowerCase().includes(query)
  );

  // Search playlists
  const matchingPlaylists = userLibrary.playlists.filter(playlist =>
    playlist.name.toLowerCase().includes(query) ||
    playlist.description.toLowerCase().includes(query)
  );

  res.json({
    tracks: matchingTracks,
    playlists: matchingPlaylists
  });
});

// Add a new playlist
app.post('/api/library/playlists', (req, res) => {
  const { name, description = '' } = req.body;
  const newPlaylist = {
    id: Date.now(),
    name,
    tracks: [],
    description,
    cover: 'imgs/playlist-default.png',
    created: new Date().toISOString().split('T')[0],
    lastModified: new Date().toISOString().split('T')[0]
  };
  userLibrary.playlists.push(newPlaylist);
  userLibrary.stats.totalPlaylists = userLibrary.playlists.length;
  res.status(201).json(newPlaylist);
});

// Delete a playlist
app.delete('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const index = userLibrary.playlists.findIndex(p => p.id === id);
  if (index !== -1) {
    userLibrary.playlists.splice(index, 1);
    res.json({ success: true });
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

// Add/Remove a song to liked songs
app.post('/api/library/liked/:songId', (req, res) => {
    const songId = parseInt(req.params.songId);
    if (!userLibrary.likedSongs.includes(songId)) {
      userLibrary.likedSongs.push(songId);
      res.status(201).json({ success: true, liked: true, songId });
    } else {
        removeLikedSong(songId, res);
    }
});

// Remove a song from liked songs
app.delete('/api/library/liked/:songId', (req, res) => {
  const songId = parseInt(req.params.songId);
  removeLikedSong(songId, res);
});

function removeLikedSong(songId, res) {
    const idx = userLibrary.likedSongs.indexOf(songId);
    if (idx !== -1) {
      userLibrary.likedSongs.splice(idx, 1);
      res.json({ success: true, liked: false, songId });
    } else {
      res.status(404).json({ error: 'Song not found in liked songs' });
    }
}

// Edit a playlist (name, description, etc.)
app.put('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const { name, description, cover } = req.body;
  const playlist = userLibrary.playlists.find(p => p.id === id);
  if (playlist) {
    if (name) playlist.name = name;
    if (description !== undefined) playlist.description = description;
    if (cover) playlist.cover = cover;
    playlist.lastModified = new Date().toISOString().split('T')[0];
    res.json({ success: true, playlist });
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

// Get a single playlist by ID
app.get('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const playlist = userLibrary.playlists.find(p => p.id === id);
  if (playlist) {
    res.json(playlist);
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

// Add a track to a playlist
app.post('/api/library/playlists/:id/tracks', (req, res) => {
  const id = parseInt(req.params.id);
  const { trackId } = req.body;
  const playlist = userLibrary.playlists.find(p => p.id === id);
  if (playlist && trackId) {
    if (!playlist.tracks.includes(trackId)) {
      playlist.tracks.push(trackId);
    }
    res.json({ success: true, playlist });
  } else {
    res.status(404).json({ error: 'Playlist not found or invalid trackId' });
  }
});

// Remove a track from a playlist
app.delete('/api/library/playlists/:id/tracks/:trackId', (req, res) => {
  const id = parseInt(req.params.id);
  const trackId = parseInt(req.params.trackId);
  const playlist = userLibrary.playlists.find(p => p.id === id);
  if (playlist) {
    const idx = playlist.tracks.indexOf(trackId);
    if (idx !== -1) {
      playlist.tracks.splice(idx, 1);
      res.json({ success: true, playlist });
    } else {
      res.status(404).json({ error: 'Track not found in playlist' });
    }
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

// ===== HOME PAGE API ENDPOINTS =====

// Get featured content for home page
app.get('/api/home/<USER>', (req, res) => {
  // Select top tracks based on plays
  const featuredTracks = Object.values(sampleTracks)
    .sort((a, b) => b.plays - a.plays)
    .slice(0, 6);

  // Select featured playlists
  const featuredPlaylists = userLibrary.playlists.slice(0, 3);

  res.json({
    tracks: featuredTracks,
    playlists: featuredPlaylists
  });
});

// Get trending content
app.get('/api/home/<USER>', (req, res) => {
  // Simulate trending based on recent plays and popularity
  const trendingTracks = Object.values(sampleTracks)
    .filter(track => track.year >= 2020) // Recent tracks
    .sort((a, b) => b.plays - a.plays)
    .slice(0, 8);

  res.json(trendingTracks);
});

// Get new releases
app.get('/api/home/<USER>', (req, res) => {
  // Get newest tracks (2022 and later)
  const newReleases = Object.values(sampleTracks)
    .filter(track => track.year >= 2022)
    .sort((a, b) => b.year - a.year)
    .slice(0, 6);

  res.json(newReleases);
});

// Get personalized recommendations based on user's library
app.get('/api/home/<USER>', (req, res) => {
  // Get user's liked songs and playlists to generate recommendations
  const userLikedTracks = userLibrary.likedSongs.map(id => sampleTracks[id]).filter(Boolean);
  const userGenres = [...new Set(userLikedTracks.map(track => track.genre))];

  // Recommend tracks from similar genres that user hasn't liked yet
  const recommendations = Object.values(sampleTracks)
    .filter(track =>
      userGenres.includes(track.genre) &&
      !userLibrary.likedSongs.includes(track.id)
    )
    .sort((a, b) => b.plays - a.plays)
    .slice(0, 6);

  res.json(recommendations);
});

// Get home page stats
app.get('/api/home/<USER>', (req, res) => {
  const totalTracks = Object.keys(sampleTracks).length;
  const totalArtists = [...new Set(Object.values(sampleTracks).map(track => track.artist))].length;
  const totalAlbums = [...new Set(Object.values(sampleTracks).map(track => track.album))].length;

  res.json({
    totalTracks: `${totalTracks}+`,
    totalArtists: `${totalArtists}+`,
    totalAlbums: `${totalAlbums}+`,
    totalUsers: '1M+' // Static for demo
  });
});

// Get user's recent activity for home page
app.get('/api/home/<USER>', (req, res) => {
  const recentTracks = userLibrary.recentlyPlayed
    .map(id => sampleTracks[id])
    .filter(Boolean)
    .slice(0, 4);

  const recentPlaylists = userLibrary.playlists
    .sort((a, b) => new Date(b.lastModified) - new Date(a.lastModified))
    .slice(0, 2);

  res.json({
    tracks: recentTracks,
    playlists: recentPlaylists
  });
});

// ===== REUSABLE ENDPOINTS FOR OTHER PAGES =====

// Get all tracks (for search, explore, etc.)
app.get('/api/tracks', (req, res) => {
  const { genre, year, limit } = req.query;
  let tracks = Object.values(sampleTracks);

  // Apply filters
  if (genre) {
    tracks = tracks.filter(track => track.genre.toLowerCase() === genre.toLowerCase());
  }
  if (year) {
    tracks = tracks.filter(track => track.year.toString() === year);
  }
  if (limit) {
    tracks = tracks.slice(0, parseInt(limit));
  }

  res.json(tracks);
});

// Get all artists
app.get('/api/artists', (req, res) => {
  const artists = [...new Set(Object.values(sampleTracks).map(track => track.artist))]
    .map(artistName => {
      const artistTracks = Object.values(sampleTracks).filter(track => track.artist === artistName);
      const totalPlays = artistTracks.reduce((sum, track) => sum + track.plays, 0);

      return {
        name: artistName,
        trackCount: artistTracks.length,
        totalPlays: totalPlays,
        topTrack: artistTracks.sort((a, b) => b.plays - a.plays)[0],
        genres: [...new Set(artistTracks.map(track => track.genre))]
      };
    })
    .sort((a, b) => b.totalPlays - a.totalPlays);

  res.json(artists);
});

// Get all genres
app.get('/api/genres', (req, res) => {
  const genres = [...new Set(Object.values(sampleTracks).map(track => track.genre))]
    .map(genreName => {
      const genreTracks = Object.values(sampleTracks).filter(track => track.genre === genreName);
      return {
        name: genreName,
        trackCount: genreTracks.length,
        topTracks: genreTracks.sort((a, b) => b.plays - a.plays).slice(0, 3)
      };
    });

  res.json(genres);
});

app.listen(PORT, () => {
  console.log(`🎵 Banshee Music API server running on port ${PORT}`);
  console.log(`📡 Available endpoints:`);
  console.log(`   Library: http://localhost:${PORT}/api/library/*`);
  console.log(`   Home: http://localhost:${PORT}/api/home/<USER>
});
